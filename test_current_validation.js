#!/usr/bin/env node

/**
 * Test script to verify the current API key validation behavior
 * This will help determine if the validation is working as expected
 */

const fs = require('fs');
const path = require('path');

// Check if the validation logic is properly implemented
function analyzeValidationImplementation() {
    console.log('🔍 Analyzing Current API Key Validation Implementation\n');
    
    // Check settings-ui.js for validation logic
    const settingsUIPath = path.join(__dirname, 'desktop/plugins/limitless/src/settings-ui.js');
    const limitlessAPIPath = path.join(__dirname, 'desktop/plugins/limitless/src/limitless-api.js');
    const settingsHTMLPath = path.join(__dirname, 'desktop/plugins/limitless/ui/settings.html');
    
    console.log('📁 Checking file existence:');
    console.log(`   settings-ui.js: ${fs.existsSync(settingsUIPath) ? '✅' : '❌'}`);
    console.log(`   limitless-api.js: ${fs.existsSync(limitlessAPIPath) ? '✅' : '❌'}`);
    console.log(`   settings.html: ${fs.existsSync(settingsHTMLPath) ? '✅' : '❌'}`);
    
    if (!fs.existsSync(settingsUIPath)) {
        console.log('❌ settings-ui.js not found - validation cannot work');
        return false;
    }
    
    // Analyze the settings-ui.js content
    const settingsUIContent = fs.readFileSync(settingsUIPath, 'utf8');
    
    console.log('\n🔍 Analyzing settings-ui.js validation logic:');
    
    // Check for key validation components
    const checks = [
        {
            name: 'API key validation call',
            pattern: /validateAPIKey\s*\(/,
            found: settingsUIContent.match(/validateAPIKey\s*\(/g)?.length || 0
        },
        {
            name: 'Validation error handling',
            pattern: /showValidationError/,
            found: settingsUIContent.match(/showValidationError/g)?.length || 0
        },
        {
            name: 'Settings save prevention on validation failure',
            pattern: /validation.*failed.*return/i,
            found: settingsUIContent.match(/validation.*failed.*return/gi)?.length || 0
        },
        {
            name: 'LimitlessAPI class usage',
            pattern: /new LimitlessAPI/,
            found: settingsUIContent.match(/new LimitlessAPI/g)?.length || 0
        },
        {
            name: 'Logger adapter creation',
            pattern: /createLoggerAdapter/,
            found: settingsUIContent.match(/createLoggerAdapter/g)?.length || 0
        }
    ];
    
    let allChecksPass = true;
    checks.forEach(check => {
        const status = check.found > 0 ? '✅' : '❌';
        console.log(`   ${status} ${check.name}: ${check.found} occurrence(s)`);
        if (check.found === 0) allChecksPass = false;
    });
    
    // Check the limitless-api.js validation method
    if (fs.existsSync(limitlessAPIPath)) {
        const apiContent = fs.readFileSync(limitlessAPIPath, 'utf8');
        console.log('\n🔍 Analyzing limitless-api.js validation method:');
        
        const apiChecks = [
            {
                name: 'validateAPIKey method exists',
                pattern: /async validateAPIKey\s*\(/,
                found: apiContent.match(/async validateAPIKey\s*\(/g)?.length || 0
            },
            {
                name: 'API endpoint construction',
                pattern: /lifelogs.*endpoint/i,
                found: apiContent.match(/lifelogs.*endpoint/gi)?.length || 0
            },
            {
                name: 'HTTP request with API key header',
                pattern: /X-API-Key.*apiKey/,
                found: apiContent.match(/X-API-Key.*apiKey/g)?.length || 0
            },
            {
                name: 'Response status checking',
                pattern: /response\.ok/,
                found: apiContent.match(/response\.ok/g)?.length || 0
            },
            {
                name: 'Error response handling',
                pattern: /response\.status.*response\.statusText/,
                found: apiContent.match(/response\.status.*response\.statusText/g)?.length || 0
            }
        ];
        
        apiChecks.forEach(check => {
            const status = check.found > 0 ? '✅' : '❌';
            console.log(`   ${status} ${check.name}: ${check.found} occurrence(s)`);
            if (check.found === 0) allChecksPass = false;
        });
    }
    
    // Check the HTML for validation status element
    if (fs.existsSync(settingsHTMLPath)) {
        const htmlContent = fs.readFileSync(settingsHTMLPath, 'utf8');
        console.log('\n🔍 Analyzing settings.html validation UI:');
        
        const htmlChecks = [
            {
                name: 'Validation status element',
                pattern: /id="validationStatus"/,
                found: htmlContent.match(/id="validationStatus"/g)?.length || 0
            },
            {
                name: 'LimitlessAPI script inclusion',
                pattern: /limitless-api\.js/,
                found: htmlContent.match(/limitless-api\.js/g)?.length || 0
            },
            {
                name: 'Settings UI script inclusion',
                pattern: /settings-ui\.js/,
                found: htmlContent.match(/settings-ui\.js/g)?.length || 0
            }
        ];
        
        htmlChecks.forEach(check => {
            const status = check.found > 0 ? '✅' : '❌';
            console.log(`   ${status} ${check.name}: ${check.found} occurrence(s)`);
            if (check.found === 0) allChecksPass = false;
        });
    }
    
    console.log('\n📋 SUMMARY:');
    if (allChecksPass) {
        console.log('✅ All validation components appear to be properly implemented');
        console.log('✅ API key validation should be working correctly');
        console.log('\n💡 If users are still experiencing issues, it might be:');
        console.log('   1. A runtime error preventing validation from executing');
        console.log('   2. Network connectivity issues during validation');
        console.log('   3. The LimitlessAPI class not being available in the browser context');
        console.log('   4. Plugin API not being properly initialized');
        console.log('\n🔧 Debugging steps:');
        console.log('   1. Open browser developer tools when testing');
        console.log('   2. Check console for JavaScript errors');
        console.log('   3. Verify network requests are being made to the Limitless API');
        console.log('   4. Test with both valid and invalid API keys');
    } else {
        console.log('❌ Some validation components are missing or incomplete');
        console.log('❌ API key validation may not be working properly');
    }
    
    return allChecksPass;
}

// Run the analysis
if (require.main === module) {
    analyzeValidationImplementation();
}

module.exports = { analyzeValidationImplementation };
