<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Limitless Plugin Validation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-good { color: #0a0; font-weight: bold; }
        .status-bad { color: #d00; font-weight: bold; }
        .status-warning { color: #f80; font-weight: bold; }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a87; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .test-pass { background: #e8f5e8; border-color: #0a0; }
        .test-fail { background: #ffe8e8; border-color: #d00; }
        .test-warning { background: #fff8e8; border-color: #f80; }
    </style>
</head>
<body>
    <h1>🐛 Debug Limitless Plugin API Key Validation</h1>
    
    <div class="debug-section">
        <h2>🔍 Environment Check</h2>
        <div id="environmentStatus">Checking environment...</div>
    </div>
    
    <div class="debug-section">
        <h2>🧪 Manual Validation Test</h2>
        <p>Test the validation behavior with different API keys:</p>
        
        <div>
            <label>API Key: </label>
            <input type="text" id="testApiKey" placeholder="Enter API key to test" value="invalid-test-key">
            <button onclick="testValidation()">Test Validation</button>
            <button onclick="testSaveSettings()">Test Save Settings</button>
        </div>
        
        <div id="testResults"></div>
    </div>
    
    <div class="debug-section">
        <h2>📋 Validation Flow Test</h2>
        <button onclick="runFullValidationTest()">Run Complete Validation Test</button>
        <div id="validationFlowResults"></div>
    </div>
    
    <div class="debug-section">
        <h2>📊 Debug Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="debugLog" class="log">Debug log initialized...\n</div>
    </div>

    <!-- Include the actual Limitless plugin scripts -->
    <script src="desktop/plugins/limitless/src/limitless-api.js"></script>
    <script>
        // Debug logging function
        function log(message, level = 'INFO') {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] [${level}] ${message}\n`;
            debugLog.textContent += logEntry;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(`[${level}] ${message}`);
        }

        // Mock Plugin API
        const mockPluginAPI = {
            network: {
                fetch: async (url, options) => {
                    log(`MOCK FETCH: ${options.method || 'GET'} ${url}`);
                    log(`MOCK FETCH Headers: ${JSON.stringify(options.headers, null, 2)}`);
                    
                    const apiKey = options.headers['X-API-Key'];
                    
                    // Simulate network delay
                    await new Promise(resolve => setTimeout(resolve, 500));
                    
                    if (!apiKey) {
                        return {
                            ok: false,
                            status: 400,
                            statusText: 'Bad Request',
                            text: async () => 'Missing API key'
                        };
                    }
                    
                    if (apiKey === 'valid-test-key-123') {
                        return {
                            ok: true,
                            status: 200,
                            statusText: 'OK',
                            json: async () => ({
                                data: { lifelogs: [] },
                                meta: { lifelogs: { count: 0 } }
                            })
                        };
                    }
                    
                    // Default to unauthorized for invalid keys
                    return {
                        ok: false,
                        status: 401,
                        statusText: 'Unauthorized',
                        text: async () => 'Invalid API key'
                    };
                }
            },
            storage: {
                saveData: (data) => {
                    log(`STORAGE SAVE: ${JSON.stringify(data, null, 2)}`);
                    return true;
                }
            }
        };

        // Logger adapter
        function createLoggerAdapter() {
            return {
                startTimer: (name) => ({
                    stop: async () => 100
                }),
                info: async (message, data) => log(`${message} ${data ? JSON.stringify(data) : ''}`),
                warn: async (message, data) => log(`${message} ${data ? JSON.stringify(data) : ''}`, 'WARN'),
                error: async (message, error) => log(`${message} ${error ? error.message || error : ''}`, 'ERROR'),
                logApiCallStart: async (url, options) => {
                    const callId = `call_${Date.now()}`;
                    log(`API CALL START: ${callId} -> ${url}`);
                    return callId;
                },
                logApiCallEnd: async (callId, response, duration) => {
                    log(`API CALL END: ${callId} - Status: ${response.status} (${duration}ms)`);
                }
            };
        }

        // Environment check
        function checkEnvironment() {
            const status = document.getElementById('environmentStatus');
            let html = '';
            
            // Check if LimitlessAPI is available
            if (typeof LimitlessAPI !== 'undefined') {
                html += '<div class="status-good">✅ LimitlessAPI class is available</div>';
            } else {
                html += '<div class="status-bad">❌ LimitlessAPI class is NOT available</div>';
            }
            
            // Check if we can create an instance
            try {
                const loggerAdapter = createLoggerAdapter();
                const api = new LimitlessAPI(mockPluginAPI, loggerAdapter);
                html += '<div class="status-good">✅ LimitlessAPI instance can be created</div>';
                
                // Check if validateAPIKey method exists
                if (typeof api.validateAPIKey === 'function') {
                    html += '<div class="status-good">✅ validateAPIKey method exists</div>';
                } else {
                    html += '<div class="status-bad">❌ validateAPIKey method is missing</div>';
                }
            } catch (error) {
                html += `<div class="status-bad">❌ Cannot create LimitlessAPI instance: ${error.message}</div>`;
            }
            
            status.innerHTML = html;
        }

        // Test validation function
        async function testValidation() {
            const apiKey = document.getElementById('testApiKey').value.trim();
            const results = document.getElementById('testResults');
            
            log(`Testing validation for API key: "${apiKey}"`);
            
            try {
                if (typeof LimitlessAPI === 'undefined') {
                    throw new Error('LimitlessAPI class not available');
                }
                
                const loggerAdapter = createLoggerAdapter();
                const api = new LimitlessAPI(mockPluginAPI, loggerAdapter);
                
                const result = await api.validateAPIKey(apiKey);
                
                let resultClass = result.success ? 'test-pass' : 'test-fail';
                let resultText = result.success ? 'PASS' : 'FAIL';
                
                results.innerHTML = `
                    <div class="test-result ${resultClass}">
                        <strong>Validation Result: ${resultText}</strong><br>
                        Success: ${result.success}<br>
                        Error: ${result.error || 'None'}<br>
                        Details: ${result.details || 'None'}
                    </div>
                `;
                
                log(`Validation result: ${result.success ? 'SUCCESS' : 'FAILED'} - ${result.error || 'No error'}`);
                
            } catch (error) {
                results.innerHTML = `
                    <div class="test-result test-fail">
                        <strong>Validation Result: ERROR</strong><br>
                        Error: ${error.message}
                    </div>
                `;
                log(`Validation error: ${error.message}`, 'ERROR');
            }
        }

        // Test save settings (simulated)
        async function testSaveSettings() {
            const apiKey = document.getElementById('testApiKey').value.trim();
            log(`Testing save settings flow for API key: "${apiKey}"`);
            
            try {
                // Simulate the settings save flow
                if (!apiKey) {
                    log('Save FAILED: API key is required', 'ERROR');
                    return;
                }
                
                // Validate first
                const loggerAdapter = createLoggerAdapter();
                const api = new LimitlessAPI(mockPluginAPI, loggerAdapter);
                const validationResult = await api.validateAPIKey(apiKey);
                
                if (!validationResult.success) {
                    log(`Save FAILED: API key validation failed - ${validationResult.error}`, 'ERROR');
                    return;
                }
                
                // If validation passed, save settings
                const formData = { apiKey, autoSync: true, syncInterval: 6 };
                const success = mockPluginAPI.storage.saveData(formData);
                
                if (success) {
                    log('Save SUCCESS: Settings saved successfully');
                } else {
                    log('Save FAILED: Storage error', 'ERROR');
                }
                
            } catch (error) {
                log(`Save ERROR: ${error.message}`, 'ERROR');
            }
        }

        // Run complete validation test
        async function runFullValidationTest() {
            const results = document.getElementById('validationFlowResults');
            results.innerHTML = '<p>Running complete validation test...</p>';
            
            const testCases = [
                { key: '', description: 'Empty API key', expectedSuccess: false },
                { key: '   ', description: 'Whitespace-only API key', expectedSuccess: false },
                { key: 'invalid-test-key', description: 'Invalid API key', expectedSuccess: false },
                { key: 'valid-test-key-123', description: 'Valid API key', expectedSuccess: true }
            ];
            
            let html = '<h3>Test Results:</h3>';
            
            for (const testCase of testCases) {
                log(`Testing: ${testCase.description}`);
                
                try {
                    const loggerAdapter = createLoggerAdapter();
                    const api = new LimitlessAPI(mockPluginAPI, loggerAdapter);
                    const result = await api.validateAPIKey(testCase.key);
                    
                    const passed = result.success === testCase.expectedSuccess;
                    const resultClass = passed ? 'test-pass' : 'test-fail';
                    
                    html += `
                        <div class="test-result ${resultClass}">
                            <strong>${passed ? '✅ PASS' : '❌ FAIL'}: ${testCase.description}</strong><br>
                            Expected: ${testCase.expectedSuccess ? 'Success' : 'Failure'}<br>
                            Actual: ${result.success ? 'Success' : 'Failure'}<br>
                            Error: ${result.error || 'None'}
                        </div>
                    `;
                    
                } catch (error) {
                    html += `
                        <div class="test-result test-fail">
                            <strong>❌ ERROR: ${testCase.description}</strong><br>
                            Error: ${error.message}
                        </div>
                    `;
                }
                
                // Wait between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            results.innerHTML = html;
            log('Complete validation test finished');
        }

        // Clear log
        function clearLog() {
            document.getElementById('debugLog').textContent = 'Debug log cleared...\n';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            log('Debug page loaded');
            checkEnvironment();
        });
    </script>
</body>
</html>
