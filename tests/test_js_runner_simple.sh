#!/bin/bash
# Simple test for the JavaScript test runner function only

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
TEST_RESULTS=()

# Extract just the run_js_test function from the main script
run_js_test() {
    local test_file=$1
    local test_name=$2
    local category=$3

    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "${YELLOW}[$category] Running: ${test_name}${NC}"

    # Check if test file exists
    if [ ! -f "$test_file" ]; then
        echo -e "${RED}✗ ERROR: Test file not found: $test_file${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - FILE NOT FOUND")
        return 1
    fi

    # Change to root directory to run Jest (where jest.config.js is located)
    local original_dir=$(pwd)
    cd ../

    # Check if Node.js and npm are available
    if ! command -v node >/dev/null 2>&1; then
        echo -e "${RED}✗ ERROR: Node.js not found. Please install Node.js to run JavaScript tests.${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - NODE.JS NOT FOUND")
        cd "$original_dir"
        return 1
    fi

    if ! command -v npm >/dev/null 2>&1; then
        echo -e "${RED}✗ ERROR: npm not found. Please install npm to run JavaScript tests.${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - NPM NOT FOUND")
        cd "$original_dir"
        return 1
    fi

    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}Installing npm dependencies...${NC}"
        if ! npm install; then
            echo -e "${RED}✗ ERROR: Failed to install npm dependencies${NC}"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            TEST_RESULTS+=("❌ [$category] $test_name - NPM INSTALL FAILED")
            cd "$original_dir"
            return 1
        fi
    fi

    # Run the specific test file with Jest
    local test_path="tests/$test_file"
    echo -e "${BLUE}   Running: npx jest \"$test_path\" --verbose${NC}"
    
    if OUTPUT=$(npx jest "$test_path" --verbose --no-cache 2>&1); then
        echo -e "${GREEN}✓ [$category] $test_name PASSED${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        TEST_RESULTS+=("✅ [$category] $test_name - PASSED")
        
        # Show test summary
        echo "$OUTPUT" | grep -E "(PASS|FAIL|Tests:|Test Suites:)" | head -5 | while read -r line; do
            echo -e "${BLUE}   $line${NC}"
        done
        
        cd "$original_dir"
        return 0
    else
        echo -e "${RED}✗ [$category] $test_name FAILED${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        TEST_RESULTS+=("❌ [$category] $test_name - FAILED")
        
        # Show first few lines of error
        echo "$OUTPUT" | head -10 | while read -r line; do
            echo -e "${RED}   $line${NC}"
        done
        
        cd "$original_dir"
        return 1
    fi
}

echo -e "${BLUE}=== Testing JavaScript Test Runner ===${NC}"

# Test the JavaScript test runner
run_js_test "SettingsValidationHTML.test.js" "Settings Validation HTML Tests" "UNIT"

echo -e "${BLUE}=== Test Summary ===${NC}"
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}✅ All JavaScript tests passed!${NC}"
    exit 0
else
    echo -e "${RED}❌ Some JavaScript tests failed.${NC}"
    exit 1
fi
