#!/bin/bash
# Simple test for the JavaScript test runner

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
TEST_RESULTS=()

# Source the main test functions
source ./run_all_tests.sh

echo -e "${BLUE}=== Testing JavaScript Test Runner ===${NC}"

# Test the JavaScript test runner
run_js_test "SettingsValidationHTML.test.js" "Settings Validation HTML Tests" "UNIT"

echo -e "${BLUE}=== Test Summary ===${NC}"
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $FAILED_TESTS"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}✅ All JavaScript tests passed!${NC}"
    exit 0
else
    echo -e "${RED}❌ Some JavaScript tests failed.${NC}"
    exit 1
fi
