const fs = require('fs');
const path = require('path');

// Polyfills for JSDOM compatibility
const { TextEncoder, TextDecoder } = require('util');
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

const { JSDOM } = require('jsdom');

describe('Settings Validation HTML Test', () => {
    let dom;
    let document;
    let window;
    let htmlContent;

    beforeAll(() => {
        // Read the HTML file
        const htmlPath = path.join(__dirname, 'test_settings_validation.html');
        htmlContent = fs.readFileSync(htmlPath, 'utf8');
        
        // Create JSDOM instance
        dom = new JSDOM(htmlContent, {
            runScripts: 'dangerously',
            resources: 'usable',
            beforeParse: (window) => {
                // Mock console methods to capture logs
                window.console = {
                    log: jest.fn(),
                    warn: jest.fn(),
                    error: jest.fn()
                };
            }
        });
        
        document = dom.window.document;
        window = dom.window;
        
        // Mock the LimitlessAPI class since it's loaded via script tag
        window.LimitlessAPI = class {
            constructor(pluginAPI, logger) {
                this.pluginAPI = pluginAPI;
                this.logger = logger;
            }
            
            async validateAPIKey(apiKey) {
                // Simulate the validation logic from the HTML
                if (!apiKey || apiKey.trim() === '') {
                    return { success: false, error: 'API key is required' };
                }
                
                if (apiKey === 'valid-test-key-123') {
                    return { success: true };
                }
                
                if (apiKey === 'network-error-key') {
                    throw new Error('Network connection failed');
                }
                
                return { success: false, error: 'Invalid API key' };
            }
        };
    });

    afterAll(() => {
        if (dom) {
            dom.window.close();
        }
    });

    describe('HTML Structure Validation', () => {
        test('should have valid HTML structure', () => {
            expect(htmlContent).toContain('<!DOCTYPE html>');
            expect(htmlContent).toContain('<html lang="en">');
            expect(htmlContent).toContain('</html>');
        });

        test('should have proper meta tags', () => {
            expect(htmlContent).toContain('<meta charset="UTF-8">');
            expect(htmlContent).toContain('<meta name="viewport"');
        });

        test('should have the correct title', () => {
            const titleElement = document.querySelector('title');
            expect(titleElement).toBeTruthy();
            expect(titleElement.textContent).toBe('Limitless Plugin Settings Validation Test');
        });
    });

    describe('Form Elements', () => {
        test('should have all required form elements', () => {
            const apiKeyInput = document.getElementById('apiKey');
            const validateBtn = document.getElementById('validateBtn');
            const saveBtn = document.getElementById('saveBtn');
            const clearLogBtn = document.getElementById('clearLogBtn');
            const runTestsBtn = document.getElementById('runTestsBtn');

            expect(apiKeyInput).toBeTruthy();
            expect(validateBtn).toBeTruthy();
            expect(saveBtn).toBeTruthy();
            expect(clearLogBtn).toBeTruthy();
            expect(runTestsBtn).toBeTruthy();
        });

        test('should have proper input attributes', () => {
            const apiKeyInput = document.getElementById('apiKey');
            
            expect(apiKeyInput.type).toBe('text');
            expect(apiKeyInput.placeholder).toBe('Enter API key to test');
        });

        test('should have proper button text', () => {
            expect(document.getElementById('validateBtn').textContent).toBe('Validate API Key');
            expect(document.getElementById('saveBtn').textContent).toBe('Save Settings');
            expect(document.getElementById('clearLogBtn').textContent).toBe('Clear Log');
            expect(document.getElementById('runTestsBtn').textContent).toBe('Run All Tests');
        });
    });

    describe('CSS Styling', () => {
        test('should include required CSS classes', () => {
            expect(htmlContent).toContain('.test-container');
            expect(htmlContent).toContain('.form-group');
            expect(htmlContent).toContain('.validation-error');
            expect(htmlContent).toContain('.validation-success');
            expect(htmlContent).toContain('.validating');
        });

        test('should have responsive design styles', () => {
            expect(htmlContent).toContain('max-width: 800px');
            expect(htmlContent).toContain('margin: 0 auto');
        });
    });

    describe('Mock Plugin API', () => {
        test('should have mock network fetch function', () => {
            // Execute the script to initialize the mock
            const scriptElements = document.querySelectorAll('script');
            const inlineScript = Array.from(scriptElements).find(script => 
                script.textContent && script.textContent.includes('mockPluginAPI')
            );
            
            expect(inlineScript).toBeTruthy();
            expect(inlineScript.textContent).toContain('mockPluginAPI');
            expect(inlineScript.textContent).toContain('network');
            expect(inlineScript.textContent).toContain('fetch');
        });

        test('should have mock storage function', () => {
            const scriptElements = document.querySelectorAll('script');
            const inlineScript = Array.from(scriptElements).find(script => 
                script.textContent && script.textContent.includes('mockPluginAPI')
            );
            
            expect(inlineScript.textContent).toContain('storage');
            expect(inlineScript.textContent).toContain('saveData');
        });
    });

    describe('Validation Function Tests', () => {
        let mockPluginAPI;
        let loggerAdapter;
        let limitlessAPI;

        beforeEach(() => {
            // Set up mocks similar to the HTML test
            mockPluginAPI = {
                network: {
                    fetch: jest.fn()
                },
                storage: {
                    saveData: jest.fn().mockReturnValue(true)
                }
            };

            loggerAdapter = {
                startTimer: jest.fn().mockReturnValue({ stop: jest.fn().mockResolvedValue(100) }),
                info: jest.fn(),
                warn: jest.fn(),
                error: jest.fn(),
                logApiCallStart: jest.fn().mockResolvedValue('call_123'),
                logApiCallEnd: jest.fn()
            };

            limitlessAPI = new window.LimitlessAPI(mockPluginAPI, loggerAdapter);
        });

        test('should reject empty API key', async () => {
            const result = await limitlessAPI.validateAPIKey('');
            
            expect(result.success).toBe(false);
            expect(result.error).toBe('API key is required');
        });

        test('should reject whitespace-only API key', async () => {
            const result = await limitlessAPI.validateAPIKey('   ');
            
            expect(result.success).toBe(false);
            expect(result.error).toBe('API key is required');
        });

        test('should accept valid API key', async () => {
            const result = await limitlessAPI.validateAPIKey('valid-test-key-123');
            
            expect(result.success).toBe(true);
            expect(result.error).toBeUndefined();
        });

        test('should reject invalid API key', async () => {
            const result = await limitlessAPI.validateAPIKey('invalid-test-key');
            
            expect(result.success).toBe(false);
            expect(result.error).toBe('Invalid API key');
        });

        test('should handle network errors', async () => {
            await expect(limitlessAPI.validateAPIKey('network-error-key'))
                .rejects
                .toThrow('Network connection failed');
        });
    });

    describe('Test Cases Coverage', () => {
        test('should include all automated test scenarios', () => {
            const testCases = [
                { key: '', description: 'Empty API key', expectedSuccess: false },
                { key: '   ', description: 'Whitespace-only API key', expectedSuccess: false },
                { key: 'invalid-test-key', description: 'Invalid API key (401 response)', expectedSuccess: false },
                { key: 'valid-test-key-123', description: 'Valid API key (200 response)', expectedSuccess: true },
                { key: 'network-error-key', description: 'Network error scenario', expectedSuccess: false }
            ];

            // Verify the HTML contains these test cases
            expect(htmlContent).toContain('Empty API key');
            expect(htmlContent).toContain('valid-test-key-123');
            expect(htmlContent).toContain('network-error-key');
        });

        test('should run all test cases programmatically', async () => {
            const testCases = [
                { key: '', expectedSuccess: false },
                { key: '   ', expectedSuccess: false },
                { key: 'invalid-test-key', expectedSuccess: false },
                { key: 'valid-test-key-123', expectedSuccess: true }
            ];

            const limitlessAPI = new window.LimitlessAPI({}, {});

            for (const testCase of testCases) {
                try {
                    const result = await limitlessAPI.validateAPIKey(testCase.key);
                    expect(result.success).toBe(testCase.expectedSuccess);
                } catch (error) {
                    // Network error case
                    expect(testCase.expectedSuccess).toBe(false);
                }
            }
        });
    });

    describe('UI Status Updates', () => {
        test('should have validation status element', () => {
            const validationStatus = document.getElementById('validationStatus');
            expect(validationStatus).toBeTruthy();
            // Note: JSDOM doesn't apply CSS styles, so we check the CSS content instead
            expect(htmlContent).toContain('display: none');
        });

        test('should have debug log element', () => {
            const debugLog = document.getElementById('debugLog');
            expect(debugLog).toBeTruthy();
            expect(debugLog.classList.contains('log')).toBe(true);
        });

        test('should have test results container', () => {
            const testResults = document.getElementById('testResults');
            expect(testResults).toBeTruthy();
        });
    });

    describe('Logging Functionality', () => {
        test('should include logging function in script', () => {
            expect(htmlContent).toContain('function log(message)');
            expect(htmlContent).toContain('debugLog.textContent');
            expect(htmlContent).toContain('console.log(message)');
        });

        test('should include logger adapter creation', () => {
            expect(htmlContent).toContain('function createLoggerAdapter()');
            expect(htmlContent).toContain('startTimer');
            expect(htmlContent).toContain('logApiCallStart');
            expect(htmlContent).toContain('logApiCallEnd');
        });
    });

    describe('Integration Points', () => {
        test('should reference limitless-api.js script', () => {
            const scriptTag = document.querySelector('script[src*="limitless-api.js"]');
            expect(scriptTag).toBeTruthy();
            expect(scriptTag.src).toContain('desktop/plugins/limitless/src/limitless-api.js');
        });

        test('should be compatible with plugin architecture', () => {
            // Test that the mock follows the expected plugin API structure
            expect(htmlContent).toContain('mockPluginAPI');
            expect(htmlContent).toContain('network:');
            expect(htmlContent).toContain('storage:');
        });
    });
});
