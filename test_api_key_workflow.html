<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Key Workflow</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .workflow-step {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007cba;
            background: #f8f9fa;
        }
        .step-pass { border-left-color: #28a745; background: #e8f5e8; }
        .step-fail { border-left-color: #dc3545; background: #ffe8e8; }
        .step-warning { border-left-color: #ffc107; background: #fff8e8; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #005a87; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .toggle-demo {
            display: inline-block;
            width: 50px;
            height: 24px;
            background: #ccc;
            border-radius: 24px;
            position: relative;
            cursor: pointer;
            margin: 0 10px;
        }
        .toggle-demo.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .toggle-demo:before {
            content: '';
            position: absolute;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: white;
            top: 3px;
            left: 3px;
            transition: 0.3s;
        }
        .toggle-demo.enabled {
            background: #28a745;
        }
        .toggle-demo.enabled:before {
            transform: translateX(26px);
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 Test API Key Required Workflow</h1>
    
    <div class="test-section">
        <h2>📋 New Workflow Requirements</h2>
        <div class="workflow-step">
            <strong>Invalid Key Workflow:</strong><br>
            1. User sees disabled "off" limitless app<br>
            2. User clicks settings<br>
            3. User provides invalid key<br>
            4. User clicks save settings<br>
            5. Backend validates key and fails<br>
            6. User sees invalid key message<br>
            7. User has no ability to turn the plugin on
        </div>
        <div class="workflow-step">
            <strong>Valid Key Workflow:</strong><br>
            1. User sees disabled "off" limitless app<br>
            2. User clicks settings<br>
            3. User provides valid key<br>
            4. User clicks save settings<br>
            5. User sees success message<br>
            6. User can close settings<br>
            7. User is able to turn plugin on or off
        </div>
    </div>
    
    <div class="test-section">
        <h2>🎮 Interactive Test</h2>
        <p>Test the new workflow behavior:</p>
        
        <div style="margin: 20px 0;">
            <strong>Plugin State:</strong>
            <span class="toggle-demo disabled" id="pluginToggle" onclick="attemptToggle()"></span>
            <span id="toggleStatus">Disabled (API key required)</span>
        </div>
        
        <div style="margin: 20px 0;">
            <button onclick="testInvalidKey()">Test Invalid API Key</button>
            <button onclick="testValidKey()">Test Valid API Key</button>
            <button onclick="resetTest()">Reset Test</button>
        </div>
        
        <div id="testResults"></div>
    </div>
    
    <div class="test-section">
        <h2>📊 Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="testLog" class="log">Test initialized...\n</div>
    </div>

    <script>
        let pluginCanEnable = false;
        let hasValidApiKey = false;
        
        function log(message) {
            const testLog = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            testLog.textContent += `[${timestamp}] ${message}\n`;
            testLog.scrollTop = testLog.scrollHeight;
        }
        
        function updateToggleState() {
            const toggle = document.getElementById('pluginToggle');
            const status = document.getElementById('toggleStatus');
            
            if (pluginCanEnable) {
                toggle.classList.remove('disabled');
                toggle.style.cursor = 'pointer';
                status.textContent = 'Ready to enable';
                log('✅ Plugin toggle enabled - user can now turn plugin on/off');
            } else {
                toggle.classList.add('disabled');
                toggle.classList.remove('enabled');
                toggle.style.cursor = 'not-allowed';
                status.textContent = 'Disabled (API key required)';
                log('❌ Plugin toggle disabled - API key required');
            }
        }
        
        function attemptToggle() {
            if (!pluginCanEnable) {
                log('🚫 Toggle attempt blocked - API key required');
                showResult('❌ Cannot enable plugin: API key required', 'step-fail');
                return;
            }
            
            const toggle = document.getElementById('pluginToggle');
            const status = document.getElementById('toggleStatus');
            
            if (toggle.classList.contains('enabled')) {
                toggle.classList.remove('enabled');
                status.textContent = 'Disabled';
                log('🔴 Plugin disabled by user');
                showResult('Plugin disabled', 'step-pass');
            } else {
                toggle.classList.add('enabled');
                status.textContent = 'Enabled';
                log('🟢 Plugin enabled by user');
                showResult('Plugin enabled successfully!', 'step-pass');
            }
        }
        
        function testInvalidKey() {
            log('🧪 Testing invalid API key workflow...');
            showResult('Testing invalid API key...', 'workflow-step');
            
            // Simulate invalid key validation
            setTimeout(() => {
                log('❌ API key validation failed: 401 Unauthorized');
                pluginCanEnable = false;
                hasValidApiKey = false;
                updateToggleState();
                showResult('❌ Invalid API key - Plugin cannot be enabled', 'step-fail');
            }, 1000);
        }
        
        function testValidKey() {
            log('🧪 Testing valid API key workflow...');
            showResult('Testing valid API key...', 'workflow-step');
            
            // Simulate valid key validation
            setTimeout(() => {
                log('✅ API key validation successful: 200 OK');
                pluginCanEnable = true;
                hasValidApiKey = true;
                updateToggleState();
                showResult('✅ Valid API key - Plugin can now be enabled!', 'step-pass');
            }, 1000);
        }
        
        function resetTest() {
            log('🔄 Resetting test state...');
            pluginCanEnable = false;
            hasValidApiKey = false;
            
            const toggle = document.getElementById('pluginToggle');
            toggle.classList.remove('enabled');
            
            updateToggleState();
            showResult('Test reset - Plugin disabled, API key required', 'workflow-step');
        }
        
        function showResult(message, className) {
            const results = document.getElementById('testResults');
            results.innerHTML = `<div class="${className}">${message}</div>`;
        }
        
        function clearLog() {
            document.getElementById('testLog').textContent = 'Log cleared...\n';
        }
        
        // Initialize
        log('🚀 API Key Workflow Test initialized');
        log('📋 Initial state: Plugin disabled, API key required');
        updateToggleState();
    </script>
</body>
</html>
